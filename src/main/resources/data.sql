-- 插入角色数据
INSERT INTO roles (name, description) VALUES 
('ROLE_USER', '普通用户'),
('ROLE_ADMIN', '管理员');

-- 插入默认管理员用户 (密码: admin123)
INSERT INTO users (username, nickname, password, email, phone, gender, enabled) VALUES
('admin', '系统管理员', '$2a$10$19S4Sr.F6g5daUcMDz.54Od9UpKhNN4ryHDwPPGASFGbipeAl8ZJW', '<EMAIL>', '13800138000', 'OTHER', true);

-- 插入测试普通用户 (密码: user123)
INSERT INTO users (username, nickname, password, email, phone, gender, birth_date, enabled) VALUES
('testuser', '测试用户', '$2a$10$1dOwbRDHpr19qyJCyKwNi.0t9rOyEI23hANcfULfwVhXlbvpd2YJO', '<EMAIL>', '13900139000', 'MALE', '1990-01-01', true);

-- 分配角色
INSERT INTO user_roles (user_id, role_id) VALUES 
(1, 2), -- admin用户分配管理员角色
(2, 1); -- testuser用户分配普通用户角色
