package com.lisong.controller;

import com.lisong.dto.ApiResponse;
import com.lisong.dto.LoginRequest;
import com.lisong.dto.RegisterRequest;
import com.lisong.dto.UserResponse;
import com.lisong.service.AuthService;
import com.lisong.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/api/auth")
@Validated
public class AuthController {

    @Autowired
    private AuthService authService;
    
    @Autowired
    private UserService userService;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<UserResponse> login(@Valid @RequestBody LoginRequest request) {
        try {
            UserResponse userResponse = authService.login(request);
            return ApiResponse.success("登录成功", userResponse);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public ApiResponse<UserResponse> register(@Valid @RequestBody RegisterRequest request) {
        try {
            UserResponse userResponse = userService.register(request);
            return ApiResponse.success("注册成功", userResponse);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/check-username")
    public ApiResponse<Boolean> checkUsername(@RequestParam String username) {
        boolean exists = userService.existsByUsername(username);
        return ApiResponse.success("查询成功", !exists);
    }

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/check-email")
    public ApiResponse<Boolean> checkEmail(@RequestParam String email) {
        boolean exists = userService.existsByEmail(email);
        return ApiResponse.success("查询成功", !exists);
    }

    /**
     * 生成密码哈希（仅用于开发测试）
     */
    @GetMapping("/hash-password")
    public ApiResponse<String> hashPassword(@RequestParam String password) {
        String hashedPassword = authService.encodePassword(password);
        return ApiResponse.success("密码哈希生成成功", hashedPassword);
    }
}
